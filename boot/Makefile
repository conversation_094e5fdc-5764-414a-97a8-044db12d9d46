# Boot Makefile for RISC-V 64 and LoongArch 64

# Default architecture
ARCH ?= riscv64

# Architecture-specific settings
ifeq ($(ARCH),riscv64)
    AS = riscv64-linux-gnu-as
    LD = riscv64-linux-gnu-ld
    OBJCOPY = riscv64-linux-gnu-objcopy
    ASFLAGS = -64
    LDFLAGS = -T $(ARCH)/linker.ld
else ifeq ($(ARCH),loongarch64)
    AS = loongarch64-linux-gnu-as
    LD = loongarch64-linux-gnu-ld
    OBJCOPY = loongarch64-linux-gnu-objcopy
    ASFLAGS = -64
    LDFLAGS = -T $(ARCH)/linker.ld
else
    $(error Unsupported architecture: $(ARCH))
endif

# Output files
BOOT_OBJ = $(ARCH)/entry64.o
BOOT_ELF = $(ARCH)/boot.elf
BOOT_BIN = $(ARCH)/boot.bin

# Default target
all: $(BOOT_BIN)

# Assemble entry point
$(BOOT_OBJ): $(ARCH)/entry64.asm
	$(AS) $(ASFLAGS) -o $@ $<

# Link boot loader
$(BOOT_ELF): $(BOOT_OBJ)
	$(LD) $(LDFLAGS) -o $@ $<

# Create binary image
$(BOOT_BIN): $(BOOT_ELF)
	$(OBJCOPY) -O binary $< $@

# Build for specific architectures
riscv64:
	$(MAKE) ARCH=riscv64

loongarch64:
	$(MAKE) ARCH=loongarch64

# Build for all architectures
all-arch: riscv64 loongarch64

# Clean targets
clean:
	rm -f riscv64/*.o riscv64/*.elf riscv64/*.bin
	rm -f loongarch64/*.o loongarch64/*.elf loongarch64/*.bin

clean-riscv64:
	rm -f riscv64/*.o riscv64/*.elf riscv64/*.bin

clean-loongarch64:
	rm -f loongarch64/*.o loongarch64/*.elf loongarch64/*.bin

# Help
help:
	@echo "Available targets:"
	@echo "  all          - Build for default architecture ($(ARCH))"
	@echo "  riscv64      - Build for RISC-V 64-bit"
	@echo "  loongarch64  - Build for LoongArch 64-bit"
	@echo "  all-arch     - Build for all architectures"
	@echo "  clean        - Clean all build artifacts"
	@echo "  clean-riscv64     - Clean RISC-V 64-bit artifacts"
	@echo "  clean-loongarch64 - Clean LoongArch 64-bit artifacts"
	@echo ""
	@echo "Usage: make [ARCH=riscv64|loongarch64] [target]"

.PHONY: all riscv64 loongarch64 all-arch clean clean-riscv64 clean-loongarch64 help
