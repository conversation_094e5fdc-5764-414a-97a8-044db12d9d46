#![no_std]
#![no_main]

use core::panic::PanicInfo;

// Re-export the boot library
pub use boot::*;

/// Entry point called from assembly
#[no_mangle]
pub extern "C" fn _start() -> ! {
    // This will be called from the assembly entry point
    rust_boot_main()
}

/// Panic handler for boot stage
#[panic_handler]
fn panic(_info: &PanicInfo) -> ! {
    // TODO: Print panic information to serial console
    loop {}
}
