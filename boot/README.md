# Boot Loader for RISC-V 64 and LoongArch 64

This directory contains the boot loader code for two 64-bit architectures:
- RISC-V 64-bit (`riscv64/`)
- LoongArch 64-bit (`loongarch64/`)

## Directory Structure

```
boot/
├── riscv64/
│   ├── entry64.asm    # RISC-V 64-bit boot entry point
│   └── linker.ld      # RISC-V 64-bit linker script
├── loongarch64/
│   ├── entry64.asm    # LoongArch 64-bit boot entry point
│   └── linker.ld      # LoongArch 64-bit linker script
├── Makefile           # Build system for both architectures
└── README.md          # This file
```

## Features

### RISC-V 64-bit Boot Loader
- Supports Sv39 virtual memory
- Multi-hart support (only hart 0 boots, others park)
- Basic identity mapping setup
- Stack initialization
- BSS section clearing

### LoongArch 64-bit Boot Loader
- Supports LoongArch virtual memory
- Multi-core support (only core 0 boots, others park)
- Basic page table setup
- Stack initialization
- BSS section clearing

## Building

### Prerequisites
You need cross-compilation toolchains for the target architectures:

For RISC-V 64-bit:
```bash
# Ubuntu/Debian
sudo apt install gcc-riscv64-linux-gnu

# Or build from source
```

For LoongArch 64-bit:
```bash
# You may need to build the toolchain from source
# or use a distribution that provides it
```

### Build Commands

Build for default architecture (RISC-V 64):
```bash
make
```

Build for specific architecture:
```bash
make riscv64
make loongarch64
```

Build for all architectures:
```bash
make all-arch
```

Clean build artifacts:
```bash
make clean
make clean-riscv64
make clean-loongarch64
```

## Memory Layout

### RISC-V 64-bit
- Base address: `0x80000000`
- Memory size: 128MB
- Stack size: 16KB

### LoongArch 64-bit
- Base address: `0x90000000`
- Memory size: 128MB
- Stack size: 16KB

## Integration with Kernel

The boot loader expects the kernel to provide a `kernel_main` function that will be called after initialization. The boot loader sets up:

1. Stack pointer
2. BSS section clearing
3. Basic virtual memory
4. Single-core execution (other cores parked)

## Customization

You can modify the following aspects:

1. **Memory layout**: Edit the `MEMORY` section in `linker.ld`
2. **Stack size**: Modify the `.space` directive in `entry64.asm`
3. **Page table setup**: Customize the `setup_page_tables` function
4. **Boot sequence**: Add additional initialization in `entry64.asm`

## Notes

- The current implementation provides basic functionality
- Virtual memory setup is simplified and may need enhancement for production use
- Error handling is minimal and should be expanded
- The boot loaders assume specific memory layouts that may need adjustment for different platforms
