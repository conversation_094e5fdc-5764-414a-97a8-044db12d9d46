use std::env;
use std::path::PathBuf;

fn main() {
    let target = env::var("TARGET").unwrap();
    let out_dir = PathBuf::from(env::var("OUT_DIR").unwrap());
    
    println!("cargo:rerun-if-changed=riscv64/entry64.asm");
    println!("cargo:rerun-if-changed=loongarch64/entry64.asm");
    println!("cargo:rerun-if-changed=riscv64/linker.ld");
    println!("cargo:rerun-if-changed=loongarch64/linker.ld");
    
    // Set linker script based on target architecture
    if target.contains("riscv64") {
        println!("cargo:rustc-link-arg=-Triscv64/linker.ld");
        println!("cargo:rustc-link-search=riscv64");
        
        // Assemble RISC-V entry point
        let status = std::process::Command::new("riscv64-linux-gnu-as")
            .args(&["-64", "-o"])
            .arg(out_dir.join("entry64.o"))
            .arg("riscv64/entry64.asm")
            .status();
            
        if let Ok(status) = status {
            if !status.success() {
                println!("cargo:warning=Failed to assemble RISC-V entry point");
            } else {
                println!("cargo:rustc-link-arg={}", out_dir.join("entry64.o").display());
            }
        } else {
            println!("cargo:warning=RISC-V assembler not found, using fallback");
        }
    } else if target.contains("loongarch64") {
        println!("cargo:rustc-link-arg=-Tloongarch64/linker.ld");
        println!("cargo:rustc-link-search=loongarch64");
        
        // Assemble LoongArch entry point
        let status = std::process::Command::new("loongarch64-linux-gnu-as")
            .args(&["-64", "-o"])
            .arg(out_dir.join("entry64.o"))
            .arg("loongarch64/entry64.asm")
            .status();
            
        if let Ok(status) = status {
            if !status.success() {
                println!("cargo:warning=Failed to assemble LoongArch entry point");
            } else {
                println!("cargo:rustc-link-arg={}", out_dir.join("entry64.o").display());
            }
        } else {
            println!("cargo:warning=LoongArch assembler not found, using fallback");
        }
    }
}
