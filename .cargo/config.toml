[build]
# Default target for RISC-V 64-bit
target = "riscv64gc-unknown-none-elf"

[target.riscv64gc-unknown-none-elf]
# Use rust-lld as the linker
linker = "rust-lld"
rustflags = [
    "-C", "link-arg=-Tboot/riscv64/linker.ld",
]

[target.loongarch64-unknown-none]
# Use rust-lld as the linker for LoongArch
linker = "rust-lld"
rustflags = [
    "-C", "link-arg=-Tboot/loongarch64/linker.ld",
]

[unstable]
# Enable unstable features if needed
build-std = ["core", "alloc"]
build-std-features = ["compiler-builtins-mem"]
