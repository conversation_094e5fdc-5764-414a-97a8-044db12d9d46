# Echos OS Makefile
# Supports RISC-V 64 and LoongArch 64 architectures

# Default architecture
ARCH ?= riscv64

# Architecture-specific settings
ifeq ($(ARCH),riscv64)
    TARGET = riscv64gc-unknown-none-elf
    QEMU = qemu-system-riscv64
    QEMU_ARGS = -machine virt -cpu rv64 -smp 1 -m 128M
    QEMU_ARGS += -bios opensbi-riscv64-generic-fw_dynamic.bin
    KERNEL_ADDR = 0x80200000
else ifeq ($(ARCH),loongarch64)
    TARGET = loongarch64-unknown-none
    QEMU = qemu-system-loongarch64
    QEMU_ARGS = -machine virt -cpu la464 -smp 1 -m 128M
    KERNEL_ADDR = 0x90000000
else
    $(error Unsupported architecture: $(ARCH))
endif

# Build settings
CARGO_TARGET_DIR = target
BOOT_ELF = $(CARGO_TARGET_DIR)/$(TARGET)/release/boot
BOOT_BIN = $(CARGO_TARGET_DIR)/$(TARGET)/release/boot.bin

# QEMU settings
QEMU_ARGS += -nographic -serial mon:stdio
QEMU_ARGS += -kernel $(BOOT_BIN)
QEMU_LOG_ARGS = -d int,mmu,pcall,cpu_reset,guest_errors
QEMU_LOG_FILE = qemu.log

# Rust build flags
RUSTFLAGS = -C link-arg=-T$(PWD)/boot/$(ARCH)/linker.ld
CARGO_BUILD_ARGS = --target $(TARGET) --release

# Default target
all: build

# Build kernel and boot
build:
	@echo "Building workspace for $(ARCH)..."
	RUSTFLAGS="$(RUSTFLAGS)" cargo build $(CARGO_BUILD_ARGS) -p boot
	@echo "Converting ELF to binary..."
	rust-objcopy --binary-architecture=$(ARCH) --strip-all \
		-O binary $(CARGO_TARGET_DIR)/$(TARGET)/release/boot $(CARGO_TARGET_DIR)/$(TARGET)/release/boot.bin

# Build boot loader for specific architecture
boot-riscv64:
	@echo "Building boot loader for RISC-V 64..."
	$(MAKE) -C boot riscv64

boot-loongarch64:
	@echo "Building boot loader for LoongArch 64..."
	$(MAKE) -C boot loongarch64

# Run in QEMU
run: build
	@echo "Starting QEMU for $(ARCH)..."
	@echo "Press Ctrl+A then X to exit QEMU"
	$(QEMU) $(QEMU_ARGS)

# Run in QEMU with logging
runlog: build
	@echo "Starting QEMU for $(ARCH) with logging..."
	@echo "Press Ctrl+A then X to exit QEMU"
	@echo "Logs will be saved to $(QEMU_LOG_FILE)"
	$(QEMU) $(QEMU_ARGS) $(QEMU_LOG_ARGS) -D $(QEMU_LOG_FILE)

# Debug with GDB
debug: build
	@echo "Starting QEMU for $(ARCH) in debug mode..."
	@echo "Connect with: gdb-multiarch -ex 'target remote :1234' $(KERNEL_ELF)"
	$(QEMU) $(QEMU_ARGS) -s -S

# Architecture-specific targets
riscv64:
	$(MAKE) ARCH=riscv64

loongarch64:
	$(MAKE) ARCH=loongarch64

# Clean targets
clean:
	@echo "Cleaning build artifacts..."
	cd kernel && cargo clean
	$(MAKE) -C boot clean
	rm -f $(QEMU_LOG_FILE)

clean-kernel:
	@echo "Cleaning kernel build artifacts..."
	cd kernel && cargo clean

clean-boot:
	@echo "Cleaning boot loader artifacts..."
	$(MAKE) -C boot clean

# Check Rust code
check:
	@echo "Checking kernel code..."
	cd kernel && cargo check $(CARGO_BUILD_ARGS)

# Format Rust code
fmt:
	@echo "Formatting kernel code..."
	cd kernel && cargo fmt

# Clippy linting
clippy:
	@echo "Running clippy on kernel code..."
	cd kernel && cargo clippy $(CARGO_BUILD_ARGS)

# Install dependencies
install-deps:
	@echo "Installing Rust targets and tools..."
	rustup target add $(TARGET)
	cargo install cargo-binutils
	rustup component add llvm-tools-preview

# Show build information
info:
	@echo "Build Configuration:"
	@echo "  Architecture: $(ARCH)"
	@echo "  Target: $(TARGET)"
	@echo "  QEMU: $(QEMU)"
	@echo "  Kernel ELF: $(KERNEL_ELF)"
	@echo "  Kernel Binary: $(KERNEL_BIN)"
	@echo "  Kernel Address: $(KERNEL_ADDR)"

# Help
help:
	@echo "Echos OS Build System"
	@echo ""
	@echo "Available targets:"
	@echo "  build        - Build kernel for current architecture"
	@echo "  run          - Build and run in QEMU"
	@echo "  runlog       - Build and run in QEMU with logging"
	@echo "  debug        - Build and run in QEMU debug mode"
	@echo "  riscv64      - Build for RISC-V 64-bit"
	@echo "  loongarch64  - Build for LoongArch 64-bit"
	@echo "  clean        - Clean all build artifacts"
	@echo "  clean-kernel - Clean kernel artifacts only"
	@echo "  clean-boot   - Clean boot loader artifacts only"
	@echo "  check        - Check Rust code"
	@echo "  fmt          - Format Rust code"
	@echo "  clippy       - Run clippy linting"
	@echo "  install-deps - Install required Rust components"
	@echo "  info         - Show build configuration"
	@echo "  help         - Show this help"
	@echo ""
	@echo "Usage: make [ARCH=riscv64|loongarch64] [target]"
	@echo ""
	@echo "Examples:"
	@echo "  make run                    # Build and run for default arch"
	@echo "  make ARCH=riscv64 run      # Build and run for RISC-V"
	@echo "  make ARCH=loongarch64 run  # Build and run for LoongArch"
	@echo "  make runlog                # Run with QEMU logging"

.PHONY: all build run runlog debug riscv64 loongarch64 clean clean-kernel clean-boot
.PHONY: check fmt clippy install-deps info help boot-riscv64 boot-loongarch64
